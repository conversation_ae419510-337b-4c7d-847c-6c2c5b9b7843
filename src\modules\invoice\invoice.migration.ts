import { Types } from 'mongoose';
import LoggerConfig from '../../common/logger/log.module';
import { connectToMongo, closeConnection, getCollection } from '../../common/database/db.module';
import { parseCSV } from '../../common/utils/csv-parser';
import { MigrationTrackerService } from '../migration/migration-tracker.service';
import { MigrationStatus } from '../migration/migration-tracker.model';
import * as crypto from 'crypto';
import { Facility, IFacility } from '../facility/facility.model';
import { IPricing, Pricing } from '../pricing/pricing.model';
import { ENUM_PRODUCT_ITEM_TYPE, ENUM_PAYMENT_METHOD, ENUM_PAYMENT_STATUS, ENUM_DISCOUNT_TYPE } from '../../common/enums/enums';
import { CsvToObjectKeyMapUser, ICsvInvoice, IInvoiceItem, IInvoicePurchase } from './invoice.interface';
import { getAllClients, getAllCustomPackage, getAllInventory, getAllPaymentMethod, getAllPricing, getAllProduct, getAllUsers } from './invoice.migration.service';
import { IClient } from '../user/client.model';
import { IUser } from '../user/user.model';
import { IPurchase, Purchase } from './purchase.model';
import { IPaymentMethod } from './payment-method.model';
import { InvoiceGenerator, InvoiceGeneratorOptions } from './invoice-generator';
import { IBillingDetails, IClientBillingDetails, IClientDetails, IPaymentDetail } from './invoice.model';
import { ICustomPackageDocument } from '../pricing/custom-package.model';
import { IProduct } from '../products/products.model';
import { IInventory } from '../products/inventory.model';


const alphanumericCharacters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';

class CsvInvoice implements ICsvInvoice {
  index: number;

  private _invoiceId!: string;
  private _invoiceDate!: string;
  private _userId!: string;
  private _billingClientId?: string;
  private _facilityId!: string;
  private _amountPaid!: string;
  private _cartDiscountType?: string;
  private _cartDiscountAmount?: string;
  private _paymentMethod!: string;
  private _paymentStatus!: string;
  private _invoiceItemId!: string;
  private _itemId!: string;
  private _itemType!: string;
  private _itemQuantity!: string;
  private _promotion?: string;
  private _itemDiscountType?: string;
  private _itemDiscountAmount?: string;
  private _startDate?: string;
  private _isBusiness?: string;
  private _employeeId?: string;
  private _voucherCode?: string;
  private _isReturnItem?: string;

  constructor(data: ICsvInvoice) {
    Object.assign(this, data);
  }
  // --- Typed Getters and Setters ---

  get invoiceId(): string {
    return this._invoiceId;
  }

  set invoiceId(val: string) {
    this._invoiceId = val;
  }

  get invoiceDate(): Date {
    return new Date(this._invoiceDate);
  }
  set invoiceDate(val: string | Date) {
    this._invoiceDate = typeof val === "string" ? val : val.toISOString();
  }

  get userId(): string {
    return this._userId;
  }
  set userId(val: string) {
    this._userId = val;
  }

  get billingClientId(): string | undefined {
    return this._billingClientId;
  }
  set billingClientId(val: string | undefined) {
    this._billingClientId = val;
  }

  get facilityId(): string {
    return this._facilityId;
  }
  set facilityId(val: string) {
    this._facilityId = val;
  }

  get amountPaid(): number {
    return parseFloat(this._amountPaid);
  }
  set amountPaid(val: number | string) {
    this._amountPaid = typeof val === "string" ? val : val.toString();
  }

  get cartDiscountType(): ENUM_DISCOUNT_TYPE | undefined {
    return this._cartDiscountType as ENUM_DISCOUNT_TYPE;
  }
  set cartDiscountType(val: ENUM_DISCOUNT_TYPE | string | undefined) {
    this._cartDiscountType = ['percentage', 'Percentage', 'PERCENTAGE'].includes(val) ? ENUM_DISCOUNT_TYPE.PERCENTAGE : ['flat', 'Flat', 'FLAT'].includes(val) ? ENUM_DISCOUNT_TYPE.FLAT : undefined;;
  }

  get cartDiscountAmount(): number | undefined {
    return this._cartDiscountAmount ? parseFloat(this._cartDiscountAmount) : undefined;
  }
  set cartDiscountAmount(val: number | string | undefined) {
    this._cartDiscountAmount = val?.toString();
  }

  get paymentMethod(): ENUM_PAYMENT_METHOD {
    return this._paymentMethod as ENUM_PAYMENT_METHOD;
  }
  set paymentMethod(val: ENUM_PAYMENT_METHOD | string) {
    this._paymentMethod = val;
  }

  get paymentStatus(): ENUM_PAYMENT_STATUS {
    return this._paymentStatus as ENUM_PAYMENT_STATUS;
  }
  set paymentStatus(val: ENUM_PAYMENT_STATUS | string) {
    this._paymentStatus = val;
  }

  get invoiceItemId(): string {
    return this._invoiceItemId;
  }
  set invoiceItemId(val: string) {
    this._invoiceItemId = val;
  }

  get itemId(): string {
    return this._itemId;
  }
  set itemId(val: string) {
    this._itemId = val;
  }

  get itemType(): ENUM_PRODUCT_ITEM_TYPE {
    return this._itemType as ENUM_PRODUCT_ITEM_TYPE;
  }
  set itemType(val: ENUM_PRODUCT_ITEM_TYPE | string) {
    this._itemType = val;
  }

  get itemQuantity(): number {
    return parseInt(this._itemQuantity, 10);
  }
  set itemQuantity(val: number | string) {
    this._itemQuantity = typeof val === "string" ? val : val.toString();
  }

  get promotion(): string | undefined {
    return this._promotion;
  }
  set promotion(val: string | undefined) {
    this._promotion = val;
  }

  get itemDiscountType(): ENUM_DISCOUNT_TYPE | undefined {
    return this._itemDiscountType as ENUM_DISCOUNT_TYPE;
  }
  set itemDiscountType(val: ENUM_DISCOUNT_TYPE | string | undefined) {
    this._itemDiscountType = ['percentage', 'Percentage', 'PERCENTAGE'].includes(val) ? ENUM_DISCOUNT_TYPE.PERCENTAGE : ['flat', 'Flat', 'FLAT'].includes(val) ? ENUM_DISCOUNT_TYPE.FLAT : undefined;
  }

  get itemDiscountAmount(): number | undefined {
    return this._itemDiscountAmount ? parseFloat(this._itemDiscountAmount) : undefined;
  }
  set itemDiscountAmount(val: number | string | undefined) {
    this._itemDiscountAmount = val?.toString();
  }

  get startDate(): Date | undefined {
    return this._startDate ? new Date(this._startDate) : undefined;
  }
  set startDate(val: string | Date | undefined) {
    if (!val) {
      this._startDate = undefined;
    } else {
      this._startDate = typeof val === "string" ? val : val.toISOString();
    }
  }

  get isBusiness(): boolean | undefined {
    if (this._isBusiness === undefined) return undefined;
    return this._isBusiness === "true" || this._isBusiness === "1";
  }
  set isBusiness(val: boolean | string | undefined) {
    if (val === undefined) {
      this._isBusiness = undefined;
    } else {
      this._isBusiness = val.toString();
    }
  }

  get employeeId(): string | undefined {
    return this._employeeId;
  }
  set employeeId(val: string | undefined) {
    this._employeeId = val;
  }

  get voucherCode(): string | undefined {
    return this._voucherCode;
  }
  set voucherCode(val: string | undefined) {
    this._voucherCode = val;
  }

  get isReturnItem(): boolean | undefined {
    if (this._isReturnItem === undefined) return undefined;
    return [true, "true", "True", "1", "yes", "y", "Y", "Yes"].includes(this._isReturnItem);
  }
  set isReturnItem(val: boolean | string | undefined) {
    if (val === undefined) {
      this._isReturnItem = undefined;
    } else {
      this._isReturnItem = val.toString();
    }
  }
}

const logger = LoggerConfig('invoice.migration');

/**
 * Migrate invoices data from CSV to MongoDB
 * @param _dbName Database name
 */
export async function migrateInvoices(_dbName: string = 'hop-migration'): Promise<void> {
  let session: any = null;
  let tracker: any = null;

  try {
    logger.log('Starting invoices migration...');

    // Connect to database
    await connectToMongo();

    // Start a session for transaction
    const mongoose = require('mongoose');
    session = await mongoose.startSession();
    session.startTransaction();

    const userIdMap: Map<string, IUser> = new Map();
    const clientIdMap: Map<string, IClient> = new Map();
    const pricingMap: Map<string, IPricing> = new Map();
    const customPackageMap: Map<string, ICustomPackageDocument> = new Map();
    const productMap: Map<string, IProduct> = new Map();
    const inventoryMap: Map<string, IInventory> = new Map();
    const paymentMethodMap: Map<string, IPaymentMethod> = new Map();

    const purchaseItems: Map<string, IPurchase[]> = new Map();
    const invoiceProductItems: Map<string, IProduct[]> = new Map();

    const customPackageItems: Map<string, ICustomPackageDocument[]> = new Map();

    const allUserIds = new Set();
    const allPricingIds = new Set();
    const allCustomPackageIds = new Set();
    const allProductIds = new Set();
    const getAllPaymentMethodIds = new Set();

    try {
      // Get invoices data from CSV
      const csvInvoicesRow = await parseCSV<ICsvInvoice>('invoices.csv', CsvToObjectKeyMapUser);
      const csvInvoices = csvInvoicesRow.map(row => {
        const invoice = new CsvInvoice(row)
        invoice.isReturnItem = row.amountPaid < 0 || row.itemQuantity < 0;
        return invoice;
      });
      const itemsGroupedByInvoiceId: Record<string, ICsvInvoice[]> = csvInvoices.reduce((acc, invoice, index) => {
        if (!acc[invoice.invoiceId]) {
          acc[invoice.invoiceId] = [];
        }
        invoice.index = index;
        acc[invoice.invoiceId].push(invoice);
        return acc;
      }, {});


      // Perform in batch of 1000
      const batchSize = 1000;
      const invoiceIds = Object.keys(itemsGroupedByInvoiceId);
      for (let i = 0; i < invoiceIds.length; i += batchSize) {
        const batchInvoiceIds = invoiceIds.slice(i, i + batchSize);
        const batchInvoices = batchInvoiceIds.map(invoiceId => itemsGroupedByInvoiceId[invoiceId]);

        const rawUserIds = new Set();
        const rawPricingIds = new Set();
        const rawCustomPackageIds = new Set();
        const rawProductIds = new Set();

        // prepare data
        for (const invoice of batchInvoices) {
          invoice.forEach(csvItem => {
            if ((csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE || csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.VOUCHER) && !allPricingIds.has(csvItem.itemId)) {
              allPricingIds.add(csvItem.itemId);
              rawPricingIds.add(csvItem.itemId);
            } else if (csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.CUSTOM_PACKAGE && !allCustomPackageIds.has(csvItem.itemId)) {
              rawCustomPackageIds.add(csvItem.itemId);
              allCustomPackageIds.add(csvItem.itemId);
            } else if (csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.PRODUCT && !allProductIds.has(csvItem.itemId)) {
              allProductIds.add(csvItem.itemId);
              rawProductIds.add(csvItem.itemId);
            } else if (!allPricingIds.has(csvItem.itemId)) {
              logger.error(`Row ${csvItem.index + 1}: Invalid item type ${csvItem.itemType} for invoice ${invoice[0].invoiceId}`);
              throw new Error(` ${csvItem.index + 1}: Invalid item type ${csvItem.itemType} for invoice ${invoice[0].invoiceId}`);
            }
            if (!getAllPaymentMethodIds.has(csvItem.paymentMethod)) {
              getAllPaymentMethodIds.add(csvItem.paymentMethod);
            }
          });
          rawUserIds.add(invoice[0].userId);
          allUserIds.add(invoice[0].userId);
          if (invoice[0].billingClientId) {
            rawUserIds.add(invoice[0].billingClientId);
            allUserIds.add(invoice[0].billingClientId);
          }
          if (invoice[0].employeeId) {
            rawUserIds.add(invoice[0].employeeId);
            allUserIds.add(invoice[0].employeeId);
          }
        }

        const [users, pricing, customPackage, products, inventory, paymentMethods]: [IUser[], IPricing[], ICustomPackageDocument[], IProduct[], IInventory[], IPaymentMethod[]] = await Promise.all([
          getAllUsers([...rawUserIds]),
          getAllPricing([...rawPricingIds]),
          getAllCustomPackage([...rawCustomPackageIds]),
          getAllProduct([...rawProductIds]),
          getAllInventory([...rawProductIds]),
          getAllPaymentMethod([...getAllPaymentMethodIds])
        ]);

        users.forEach(u => {
          userIdMap.set(u._id.toString(), u);
          userIdMap.set(u.id.toString(), u);
          userIdMap.set(u.email.toString(), u);
          userIdMap.set(u.mobile.toString(), u);
        });

        pricing.forEach(p => {
          pricingMap.set(p._id.toString(), p);
          if (p.id) {
            pricingMap.set(p.id.toString(), p);
          }
        });

        customPackage.forEach(p => {
          customPackageMap.set(p._id.toString(), p);
          if (p.id) {
            customPackageMap.set(p.id.toString(), p);
          }
        });

        products.forEach(p => {
          productMap.set(p._id.toString(), p);
          if (p.id) {
            productMap.set(p.id.toString(), p);
          }
        });

        inventory.forEach(p => {
          inventoryMap.set(p._id.toString(), p);
          inventoryMap.set(p.productId.toString(), p);
          if (p.id) {
            inventoryMap.set(p.id.toString(), p);
          }
        });

        paymentMethods.forEach(p => {
          paymentMethodMap.set(p.shortId, p);
        });

        const clients = await getAllClients(users.map(u => u._id));
        clients.forEach(c => {
          clientIdMap.set(c.userId.toString(), c);
        });

        // >> Process to one invoice data
        for (const invoiceRawData of batchInvoices) {

          const facility: IFacility = global.facilityMap.get(invoiceRawData[0].facilityId) ?? global.facilityMap.values().next()?.value;
          if (!facility) {
            logger.error(`Row ${invoiceRawData[0].index + 1}: Facility not found for invoice ${invoiceRawData[0].invoiceId}`);
            throw new Error(`Row ${invoiceRawData[0].index + 1}: Facility not found for invoice ${invoiceRawData[0].invoiceId}`);
          }
          const user = userIdMap.get(invoiceRawData[0].userId);
          if (!user) {
            logger.error(`Row ${invoiceRawData[0].index + 1}: User not found for invoice ${invoiceRawData[0].invoiceId}`);
            throw new Error(`Row ${invoiceRawData[0].index + 1}: User not found for invoice ${invoiceRawData[0].invoiceId}`);
          }
          const paymentMethod = paymentMethodMap.get(invoiceRawData[0].paymentMethod);
          if (!paymentMethod) {
            logger.error(`Row ${invoiceRawData[0].index + 1}: Payment method not found for invoice ${invoiceRawData[0].invoiceId}`);
            throw new Error(`Row ${invoiceRawData[0].index + 1}: Payment method not found for invoice ${invoiceRawData[0].invoiceId}`);
          }
          const amount = Number(invoiceRawData[0].amountPaid);
          // if (amount <= 0 || isNaN(amount)) {
          //   logger.error(`Row ${invoiceRawData[0].index + 1}: Invalid amount for invoice ${amount}`);
          //   throw new Error(`Row ${invoiceRawData[0].index + 1}: Invalid amount for invoice ${amount}`);
          // }

          let paymentDetails: IPaymentDetail[] = [];
          if (invoiceRawData[0].paymentStatus === ENUM_PAYMENT_STATUS.COMPLETED) {
            const paymentMethod = paymentMethodMap.get(invoiceRawData[0].paymentMethod);
            if (!paymentMethod) {
              logger.error(`Row ${invoiceRawData[0].index + 1}: Payment method not found for invoice ${invoiceRawData[0].invoiceId}`);
              throw new Error(`Row ${invoiceRawData[0].index + 1}: Payment method not found for invoice ${invoiceRawData[0].invoiceId}`);
            }

            paymentDetails = [{
              paymentMethod: paymentMethod.shortId,
              paymentMethodId: paymentMethod._id as Types.ObjectId,
              amount: amount,
              paymentDate: new Date(invoiceRawData[0].invoiceDate || new Date()),
              paymentStatus: invoiceRawData[0].paymentStatus,
              paymentGateway: paymentMethod.shortId,
            }];
          }

          const clientDetails: IClientDetails = {
            customerId: user._id as Types.ObjectId,
            name: user.name,
            email: user.email,
            phone: user.mobile,
          }

          const isBusiness = invoiceRawData[0].isBusiness;
          const billingUserId = invoiceRawData[0].billingClientId || user._id.toString();
          const billingUser = userIdMap.get(billingUserId);
          const billingClient = clientIdMap.get(billingUser?._id?.toString());
          const city = global.cityMap.get(billingClient?.address?.city?.toString());
          const state = global.stateMap.get(billingClient?.address?.state?.toString());
          const billingAddress = isBusiness ? billingClient?.businessAddress : billingClient?.address;
          // if (!city) {
          //   logger.error(`Row ${invoiceRawData[0].index + 1}: City not found for invoice ${invoiceRawData[0].invoiceId}`);
          //   throw new Error(`Row ${invoiceRawData[0].index + 1}: City not found for invoice ${invoiceRawData[0].invoiceId}`);
          // }
          if (!state) {
            logger.error(`Row ${invoiceRawData[0].index + 1}: State not found for invoice ${invoiceRawData[0].invoiceId}`);
            throw new Error(`Row ${invoiceRawData[0].index + 1}: State not found for invoice ${invoiceRawData[0].invoiceId}`);
          }
          if (!billingUser?.email && !billingUser?.mobile) {
            logger.error(`Row ${invoiceRawData[0].index + 1}: Email or phone not found for invoice ${invoiceRawData[0].invoiceId}`);
            throw new Error(`Row ${invoiceRawData[0].index + 1}: Email or phone not found for invoice ${invoiceRawData[0].invoiceId}`);
          }
          // if (!billingAddress) {
          //   logger.error(`Row ${invoiceRawData[0].index + 1}: Billing address not found for invoice ${invoiceRawData[0].invoiceId}`);
          //   throw new Error(`Row ${invoiceRawData[0].index + 1}: Billing address not found for invoice ${invoiceRawData[0].invoiceId}`);
          // }
          const clientBillingDetails: IClientBillingDetails = {
            customerId: billingUser._id as Types.ObjectId,
            name: billingUser.name,
            addressLine1: billingAddress.addressLine1 || "Address Line 1",
            addressLine2: billingAddress.addressLine2,
            postalCode: billingAddress.postalCode || 123456,
            stateId: billingAddress.state as Types.ObjectId,
            cityId: city?._id as Types.ObjectId,
            cityName: city?.name,
            stateName: state.name,
            email: billingUser.email,
            phone: billingUser.mobile,
            gstNumber: billingAddress.gstNumber,
            utCode: state.gstCode,
          }

          const facilityCity = global.cityMap.get(facility?.address?.city?.toString());
          const facilityState = global.stateMap.get(facility?.address?.state?.toString());
          const employeeId = userIdMap.get(invoiceRawData[0].employeeId)?._id as Types.ObjectId ?? global.config.organizationId;

          const facilityBillingDetails: IBillingDetails = {
            facilityName: facility.facilityName,
            billingName: facility.billingDetails?.billingName ?? "Facility Name",
            gstNumber: facility.billingDetails?.gstNumber ?? "",
            email: facility.email || "<EMAIL>",
            phone: facility.mobile || "1234567890",
            addressLine1: facility?.billingDetails?.addressLine1 || "Address Line 1",
            addressLine2: facility?.billingDetails?.addressLine2 || "",
            postalCode: facility?.billingDetails?.postalCode || 123456,
            cityId: facilityCity?._id as Types.ObjectId,
            cityName: facilityCity?.name,
            stateId: facilityState._id as Types.ObjectId,
            stateName: facilityState.name,
            utCode: facilityState.gstCode,
          }

          // if (!facilityCity) {
          //   logger.error(`Row ${invoiceRawData[0].index + 1}: City not found for facility of invoice ${invoiceRawData[0].invoiceId}`);
          //   throw new Error(`Row ${invoiceRawData[0].index + 1}: City not found for facility of invoice ${invoiceRawData[0].invoiceId}`);
          // }
          if (!facilityState) {
            logger.error(`Row ${invoiceRawData[0].index + 1}: State not found for facility of invoice ${invoiceRawData[0].invoiceId}`);
            throw new Error(`Row ${invoiceRawData[0].index + 1}: State not found for facility of invoice ${invoiceRawData[0].invoiceId}`);
          }
          if (!facilityBillingDetails.utCode) {
            logger.error(`Row ${invoiceRawData[0].index + 1}: UT code not found for invoice ${invoiceRawData[0].invoiceId}`);
            throw new Error(`Row ${invoiceRawData[0].index + 1}: UT code not found for invoice ${invoiceRawData[0].invoiceId}`);
          }
          if (!facilityBillingDetails.stateId) {
            logger.error(`Row ${invoiceRawData[0].index + 1}: State not found for invoice ${invoiceRawData[0].invoiceId}`);
            throw new Error(`Row ${invoiceRawData[0].index + 1}: State not found for invoice ${invoiceRawData[0].invoiceId}`);
          }
          if (!facilityBillingDetails.email && !facilityBillingDetails.phone) {
            logger.error(`Row ${invoiceRawData[0].index + 1}: Email or phone not found for invoice ${invoiceRawData[0].invoiceId}`);
            throw new Error(`Row ${invoiceRawData[0].index + 1}: Email or phone not found for invoice ${invoiceRawData[0].invoiceId}`);
          }

          const invoiceOptions: InvoiceGeneratorOptions = {
            userId: billingUser._id.toString(),
            organizationId: global.config.organizationId.toString(),
            facilityId: facility._id.toString(),
            createdBy: employeeId.toString(),
            paymentBy: employeeId.toString(),
            invoiceNumber: Number(invoiceRawData[0].invoiceId),
            orderId: Number(invoiceRawData[0].invoiceId),
            platform: 'migration',
            date: invoiceRawData[0].invoiceDate,
            isInclusiveofGst: !!global.config.organizationSetting?.isInclusiveofGst,
            billingAddressId: billingUser._id.toString(),
            clientDetails,
            clientBillingDetails,
            billingDetails: facilityBillingDetails,
            paymentDetails: paymentDetails,
            isForBusiness: isBusiness,
            cartDiscount: Number(invoiceRawData[0].cartDiscountAmount) || 0,
            cartDiscountType: invoiceRawData[0].cartDiscountType,
          }

          const invoice = new InvoiceGenerator(invoiceOptions);

          invoiceRawData.forEach(csvItem => {
            const isReturnItem = csvItem.amountPaid < 0 || csvItem.itemQuantity < 0;
            csvItem.amountPaid = Math.abs(csvItem.amountPaid);
            csvItem.itemQuantity = Math.abs(csvItem.itemQuantity);
            if (!isReturnItem && csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE || csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.VOUCHER) {
              const item = pricingMap.get(csvItem.itemId);
              if (!item) {
                logger.error(`Row ${csvItem.index + 1}: Service or voucher Item not found for invoice ${csvItem.invoiceId}`);
                throw new Error(`Row ${csvItem.index + 1}: Service or voucher Item not found for invoice ${csvItem.invoiceId}`);
              }
              item.price = csvItem.amountPaid || item.price;
              invoice.addServiceOrVoucherItem(item,
                Number(csvItem.itemQuantity), {
                startDate: csvItem.startDate ?? undefined,
                discountType: csvItem.itemDiscountType,
                discountValue: csvItem.itemDiscountAmount,
                promotionLabel: csvItem.promotion,
                promotionLabelKey: csvItem.promotion,
              });
            } else if (!isReturnItem && csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.CUSTOM_PACKAGE) {
              const item = customPackageMap.get(csvItem.itemId);
              if (!item) {
                logger.error(`Row ${csvItem.index + 1}: Custom package Item not found for invoice ${csvItem.invoiceId}`);
                throw new Error(`Row ${csvItem.index + 1}: Custom package Item not found for invoice ${csvItem.invoiceId}`);
              }
              item.unitPrice = csvItem.amountPaid || item.unitPrice;
              invoice.addCustomPackageItem(item, {
                discountType: csvItem.itemDiscountType,
                discountValue: csvItem.itemDiscountAmount,
                promotionLabel: csvItem.promotion,
                promotionLabelKey: csvItem.promotion,
              });
            } else if (!isReturnItem && csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.PRODUCT) {
              const item = productMap.get(csvItem.itemId);
              const inventory = inventoryMap.get(csvItem.itemId);
              if (!item) {
                logger.error(`Row ${csvItem.index + 1}: Product Item not found for invoice ${csvItem.invoiceId}`);
                throw new Error(`Row ${csvItem.index + 1}: Product Item not found for invoice ${csvItem.invoiceId}`);
              }
              if (!inventory) {
                logger.error(`Row ${csvItem.index + 1}: Product not found for invoice ${csvItem.invoiceId}`);
                throw new Error(`Row ${csvItem.index + 1}: Product not found for invoice ${csvItem.invoiceId}`);
              }
              inventory.salePrice = csvItem.amountPaid || inventory.salePrice;
              invoice.addProductItem(item, inventory, csvItem.itemQuantity, undefined, {
                discountType: csvItem.itemDiscountType,
                discountValue: csvItem.itemDiscountAmount,
                promotionLabel: csvItem.promotion,
                promotionLabelKey: csvItem.promotion,
              });
            }
            // Return service
            // Return custom package
            else if (isReturnItem && csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.SERVICE) {
              const item = pricingMap.get(csvItem.itemId);
              if (!item) {
                logger.error(`Row ${csvItem.index + 1}: Item not found for invoice ${csvItem.invoiceId}`);
                throw new Error(`Row ${csvItem.index + 1}: Item not found for invoice ${csvItem.invoiceId}`);
              }
              invoice.returnServiceItem(item,
                csvItem.amountPaid, Number(csvItem.itemQuantity),
                {
                  startDate: csvItem.startDate ?? undefined,
                  discountType: csvItem.itemDiscountType,
                  discountValue: csvItem.itemDiscountAmount,
                  promotionLabel: csvItem.promotion,
                  promotionLabelKey: csvItem.promotion,
                });
            }
            else if (isReturnItem && csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.CUSTOM_PACKAGE) {
              const item = customPackageMap.get(csvItem.itemId);
              if (!item) {
                logger.error(`Row ${csvItem.index + 1}: Item not found for invoice ${csvItem.invoiceId}`);
                throw new Error(`Row ${csvItem.index + 1}: Item not found for invoice ${csvItem.invoiceId}`);
              }
              invoice.returnCustomPackageItem(item, csvItem.amountPaid, {
                discountType: csvItem.itemDiscountType,
                discountValue: csvItem.itemDiscountAmount,
                promotionLabel: csvItem.promotion,
                promotionLabelKey: csvItem.promotion,
              });
            }
            // Return product
            else if (isReturnItem && csvItem.itemType === ENUM_PRODUCT_ITEM_TYPE.PRODUCT) {
              const item = productMap.get(csvItem.itemId);
              const inventory = inventoryMap.get(csvItem.itemId);
              if (!item) {
                logger.error(`Row ${csvItem.index + 1}: Item not found for invoice ${csvItem.invoiceId}`);
                throw new Error(`Row ${csvItem.index + 1}: Item not found for invoice ${csvItem.invoiceId}`);
              }
              if (!inventory) {
                logger.error(`Row ${csvItem.index + 1}: Product not found for invoice ${csvItem.invoiceId}`);
                throw new Error(`Row ${csvItem.index + 1}: Product not found for invoice ${csvItem.invoiceId}`);
              }
              invoice.returnProductItem(item, inventory,
                csvItem.amountPaid, csvItem.itemQuantity, undefined, {
                discountType: csvItem.itemDiscountType,
                discountValue: csvItem.itemDiscountAmount,
                promotionLabel: csvItem.promotion,
                promotionLabelKey: csvItem.promotion,
              });
            }

          });

          // << end of one invoice process
          invoice.execute();
          await invoice.save({ session });
          logger.info(invoice.invoice);
        }
      }


      // Commit the transaction
      await session.commitTransaction();
      logger.log('Transaction committed successfully');

    } catch (error) {
      // Abort the transaction on error
      logger.error('Error in migration process:', error);
      if (session) {
        await session.abortTransaction();
      }

      // Update migration tracker with failure
      // if (tracker) {
      //   await MigrationTrackerService.completeMigration(
      //     tracker._id.toString(),
      //     MigrationStatus.FAILED
      //   );
      // }

      logger.error('Error in migration process:', error);
      throw error;
    }
  } catch (error) {
    logger.error('Error migrating invoices:', error);
  } finally {
    // End the session
    if (session) {
      session.endSession();
    }
    // Close the connection
    await closeConnection();
  }
}


function shuffleString(str: string): string {
  let arr = str.split('');
  for (let i = arr.length - 1; i > 0; i--) {
    let j = Math.floor(Math.random() * (i + 1));
    [arr[i], arr[j]] = [arr[j], arr[i]];
  }
  return arr.join('');
}

/**
 * Generates a secure code for a voucher.
 * @returns A secure code.
 */
export async function generateSecureCode(attempts: number = 0): Promise<string> {
  const length = 12; // Fixed length of 12 characters
  let result = '';
  const bytes = crypto.randomBytes(length);

  for (let i = 0; i < length; i++) {
    const index = bytes[i] % shuffleString(alphanumericCharacters).length;
    result += alphanumericCharacters[index];
  }

  // Add hyphens every 4 characters for readability.
  let formattedResult = result.replace(/(.{4})/g, '$1-').slice(0, -1);
  const count = await Purchase.countDocuments({ voucherCode: formattedResult }).lean();
  if (count > 0) {
    if (attempts >= 10) {
      throw new Error('Failed to generate unique voucher code after 10 attempts.');
    }
    return generateSecureCode(attempts + 1);
  }
  return formattedResult;
}
